{"name": "tehseen.io", "version": "0.1.0", "private": true, "scripts": {"clean": "git clean -xdf node_modules .next .velite", "dev:next": "next dev", "dev:velite": "velite dev", "dev": "run-p dev:* | pino-pretty", "build:next": "next build", "build:content": "velite --clean", "start": "next start", "lint": "biome lint", "velite": "velite", "db:push": "drizzle-kit push:pg --config=drizzle.config.ts", "db:migrate": "drizzle-kit generate:pg --config=drizzle.config.ts", "db:studio": "drizzle-kit studio", "format": "biome format --write . --log-level warn", "typecheck": "tsc --noEmit", "lint-staged": "lint-staged", "prepare": "husky"}, "dependencies": {"@auth/drizzle-adapter": "^1.0.1", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.2", "@vercel/postgres": "^0.9.0", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "drizzle-orm": "^0.31.2", "geist": "^1.3.0", "lucide-react": "^0.400.0", "motion": "^12.6.3", "next": "14.2.4", "next-auth": "^5.0.0-beta.17", "next-themes": "^0.3.0", "nextjs-toploader": "^1.6.12", "pino": "^9.2.0", "react": "^18", "react-dom": "^18", "rehype-autolink-headings": "^7.1.0", "rehype-pretty-code": "^0.13.2", "rehype-slug": "^6.0.0", "sonner": "^1.5.0", "sugar-high": "^0.7.0", "tailwind-merge": "^2.3.0", "tailwindcss-animate": "^1.0.7", "tiny-invariant": "^1.3.3", "zod": "^3.23.8"}, "devDependencies": {"@biomejs/biome": "1.9.4", "@next/env": "^12.3.4", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "autoprefixer": "^10.4.19", "drizzle-kit": "^0.22.8", "husky": "^9.0.11", "lint-staged": "^15.2.7", "npm-run-all": "^4.1.5", "pg": "^8.12.0", "pino-pretty": "^11.2.1", "postcss": "^8", "shiki": "^1.17.6", "tailwindcss": "^3.4.4", "tsc-files": "^1.1.4", "typescript": "^5.5.3", "velite": "^0.1.1"}, "license": "MIT", "author": "<PERSON> <contact.te<PERSON><PERSON><PERSON><PERSON>@gmail.com>"}