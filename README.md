# tehseen.io

- **Framework**: [Next.js](https://nextjs.org/)
- **Styling**: [Tailwind CSS](https://tailwindcss.com)
  <!-- - **Database**: [Postgres](https://vercel.com/postgres) -->
  <!-- - **Authentication**: [NextAuth.js](https://next-auth.js.org) -->
- **Deployment**: [Vercel](https://vercel.com)
<!-- - **Analytics**: [Vercel Analytics](https://vercel.com/analytics) -->

## Running Locally

This application requires Node.js v18.17+.

```bash
git clone https://github.com/MdTehseen<PERSON>han/tehseen.io.git
cd tehseen.io
npm install
# npm run setup # Remove all of my personal information
npm run dev
```

Create a `.env` file similar to [`.env.example`](https://github.com/MdTehseen<PERSON>han/tehseen.io/blob/main/.env.example).

## License

1. You are free to use this code as inspiration.
2. Please do not copy it directly.
3. Crediting the author is appreciated.

<!-- Please remove all of my personal information (blog posts, images, etc.) by running `npm run setup`. -->
