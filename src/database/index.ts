import { sql } from '@vercel/postgres';
import { type VercelPgDatabase, drizzle } from 'drizzle-orm/vercel-postgres';

import * as schema from '@/database/schema';

declare global {
  // eslint-disable-next-line no-var -- only var works here
  var database: VercelPgDatabase<typeof schema> | undefined;
}

let db: VercelPgDatabase<typeof schema>;

if (process.env.NODE_ENV === 'production') {
  db = drizzle(sql, { schema });
} else {
  if (!global.database) {
    global.database = drizzle(sql, { schema });
  }
  db = global.database;
}

export { db };
