import Link from 'next/link';

import { SendMessageInput } from '@/components/send-message-input';
import { buttonVariants } from '@/components/ui/button';

import { SiteConfig } from '@/configuration';
import { getSession } from '@/utils/auth';

export const Footer = async () => {
  const session = await getSession();
  const sessionExists = Boolean(session?.user);

  return (
    <footer className="mt-auto space-y-5">
      <h1 className="font-medium">Get in Touch</h1>

      <SendMessageInput sessionExists={sessionExists} />

      <div className="flex gap-4">
        <div>
          {Object.values(SiteConfig.socials).map(({ title, link, Icon }) => {
            return (
              <Link
                key={title}
                href={link}
                target="_blank"
                className={buttonVariants({
                  variant: 'ghost',
                  size: 'icon',
                })}
              >
                <Icon />
              </Link>
            );
          })}
        </div>
      </div>
    </footer>
  );
};
