import { type FC, useRef } from 'react';

interface HackerWordsProps {
  children: string;
}
const letters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';

export const HackerWords: FC<HackerWordsProps> = ({ children }) => {
  const textRef = useRef(null);

  const onHover: React.MouseEventHandler<HTMLDivElement> = (event) => {
    // let iteration = 0;
    // let interval = null;
    // clearInterval(interval);
    // interval = setInterval(() => {
    //   children = children.innerText
    //     .split("")
    //     .map((letter, index) => {
    //       if(index < iteration) {
    //         return event.target.dataset.value[index];
    //       }
    //       return letters[Math.floor(Math.random() * 26)]
    //     })
    //     .join("");
    //   if(iteration >= event.target.dataset.value.length){
    //     clearInterval(interval);
    //   }
    //   iteration += 1 / 3;
    // }, 30);
  };

  return (
    <div ref={textRef} onMouseOver={onHover}>
      {children}
    </div>
  );
};
