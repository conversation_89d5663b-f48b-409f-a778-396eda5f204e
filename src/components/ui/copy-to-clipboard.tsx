'use client';

import { CheckIcon, CopyIcon } from 'lucide-react';
import React, { useState } from 'react';
import { toast } from 'sonner';

import { cn } from '@/utils';

interface CopyToClipboardProps {
  code: string;
  className?: string;
}

export const CopyToClipboard = ({ code, className }: CopyToClipboardProps) => {
  const [copied, setCopied] = useState(false);

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(code);
      toast.success('Copied to clipboard');
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (error) {
      console.error('Error copying to clipboard', error);
    }
  };

  return (
    <button
      type="button"
      onClick={copyToClipboard}
      className={cn(
        'relative p-2 rounded-sm outline-none overflow-hidden',
        copied && 'pointer-events-none',
        className
      )}
      aria-label={copied ? 'Copied' : 'Copy to clipboard'}
    >
      <CopyIcon
        className={cn(
          'absolute inset-0 transition-smooth text-gray-400 size-4',
          copied ? '-translate-y-full opacity-0' : 'translate-y-0 opacity-100'
        )}
      />
      <CheckIcon
        className={cn(
          'absolute inset-0 transition-smooth text-gray-400 size-4',
          copied ? 'translate-y-0 opacity-100' : 'translate-y-full opacity-0'
        )}
      />
    </button>
  );
};
