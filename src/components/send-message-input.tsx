'use client';

import { signIn } from 'next-auth/react';
import { useCallback } from 'react';
import { toast } from 'sonner';

import { GoogleIcon } from '@/components/icons/google';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';

interface SendMessageInputProps {
  sessionExists?: boolean;
}

export function SendMessageInput(props: SendMessageInputProps) {
  const { sessionExists } = props;

  const handleSignInWithGoogle = useCallback(() => {
    const promise = signIn('google');
    toast.promise(promise, {
      loading: 'Authenticating...',
      error: (err: Error) => `Authentication error. ${err.message}`,
    });
  }, [signIn]);

  const handleSendMessage = useCallback(() => {}, []);

  const variant = !sessionExists ? 'transparent' : 'default';
  const onClick = !sessionExists ? handleSignInWithGoogle : handleSendMessage;

  return (
    <Input placeholder="Send me a message" disabled={!sessionExists}>
      <Button
        size="xs"
        type="button"
        className="gap-1 mr-1"
        variant={variant}
        onClick={onClick}
      >
        {getButtonContent(sessionExists)}
      </Button>
    </Input>
  );
}

function getButtonContent(sessionExists = false) {
  if (!sessionExists) {
    return (
      <>
        <GoogleIcon />
        <span>Sign in</span>
      </>
    );
  }

  return 'Send';
}
