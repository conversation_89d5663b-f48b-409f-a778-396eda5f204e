import { ArrowRightIcon, MapPinIcon } from 'lucide-react';
import Link from 'next/link';

import {
  CardList,
  CardListItem,
  CardListItemDescription,
  CardListItemTitle,
  HoverLine,
} from '@/components/card-list';
import { FlipWords } from '@/components/ui/flip-words';
import { PingDot } from '@/components/ui/ping-dot';

import { If } from '@/components/ui/if';
import { SiteConfig } from '@/configuration';
import { experience } from '@/constants/experience';
import { sortBlogsDesc } from '@/utils';
import { formatBlogDate } from '@/utils/date';
import { posts, projects } from '@/velite';

const words = [
  'full-stack developer',
  'freelancer',
  'quick learner',
  'tech enthusiast',
];

export default function Home() {
  return (
    <div className="space-y-10">
      <div className="leading-relaxed text-secondary-foreground space-y-3">
        <div className="text-2xl font-medium overflow-hidden">
          I&apos;m a <FlipWords words={words} />{' '}
        </div>

        <p className="space-x-1">
          <span>
            A passionate full-stack developer who loves to build challenging
            apps
          </span>
          <span>•</span>
          <span>
            Born and raised in the &ldquo;City of Lights&rdquo;, Karachi
            (Pakistan)
          </span>
          <span>•</span>
          <span>Actively seeking new opportunities</span>
          {/* <span>Works at{' '}<Link
            href="https://govmaven.com"
            target="_blank"
            className={cn(buttonVariants({ variant: 'link', size: 'link' }))}
          >
            GovMaven
          </Link>{' '}
          as a Software Engineer.</span> */}
        </p>

        <div className="flex gap-2 items-center">
          <PingDot color="green" size="sm" />
          <span className="text-sm">Available for new opportunities</span>
        </div>
      </div>

      <div>
        <div className="flex mb-2">
          <h1 className="font-medium">Experience</h1>

          {/* <Link
            href={SiteConfig.paths.experience.link}
            className="flex items-center transition-smooth text-secondary-foreground hover:text-accent-foreground"
          >
            <span>View All</span>

            <ArrowRightIcon className="h-4" />
          </Link> */}
        </div>

        <div>
          {experience.map((exp) => (
            <ExperienceCard key={exp.title} experience={exp} />
          ))}
        </div>
      </div>

      <div>
        <div className="flex justify-between mb-2">
          <h1 className="font-medium">Projects</h1>

          <Link
            href={SiteConfig.paths.projects.link}
            className="flex items-center transition-smooth text-secondary-foreground hover:text-accent-foreground"
          >
            <span>View All</span>

            <ArrowRightIcon className="h-4" />
          </Link>
        </div>
        <CardList>
          {projects.map(
            ({ id, title, description, projectLink, githubLink }) => (
              <CardListItem key={id} href={projectLink} githubLink={githubLink}>
                <CardListItemTitle>{title}</CardListItemTitle>
                <CardListItemDescription>{description}</CardListItemDescription>
              </CardListItem>
            )
          )}
        </CardList>
      </div>

      <div>
        <div className="flex justify-between mb-2">
          <h1 className="font-medium">Blog</h1>

          <Link
            href={SiteConfig.paths.blog.link}
            className="flex items-center transition-smooth text-secondary-foreground hover:text-accent-foreground"
          >
            <span>View All</span>

            <ArrowRightIcon className="h-4" />
          </Link>
        </div>

        <CardList>
          {posts
            .slice(0, 3)
            .sort(sortBlogsDesc)
            .map(({ id, permalink, title, date }) => (
              <CardListItem
                key={id}
                href={permalink}
                className="sm:flex-row sm:gap-4 sm:p-0 sm:mx-0 sm:hover:bg-transparent sm:items-center sm:justify-between text-secondary-foreground sm:hover:text-accent-foreground"
              >
                <h3 className="transition-smooth max-w-sm overflow-ellipsis text-accent-foreground sm:text-secondary-foreground sm:group-hover:text-accent-foreground">
                  {title}
                </h3>
                <HoverLine />
                <span>{formatBlogDate(date)}</span>
              </CardListItem>
            ))}
        </CardList>
      </div>
    </div>
  );
}

function ExperienceCard(props: { experience: (typeof experience)[number] }) {
  if (!props.experience.published) {
    return null;
  }
  const {
    title,
    position,
    startYear,
    endYear,
    location,
    locationType,
    description,
  } = props.experience;
  return (
    <div className="py-3 flex flex-col gap-2 text-secondary-foreground">
      <div className="flex gap-1.5 items-center">
        <span className="text-foreground">{title}</span>
        <span>•</span>
        <span className="text-sm">{position}</span>
        <span>•</span>
        <span className="text-sm">
          <If condition={startYear === endYear}>{startYear}</If>
          <If condition={startYear !== endYear}>
            {startYear} - {endYear ? endYear : 'Present'}
          </If>
        </span>
      </div>

      {location && (
        <div className="flex gap-1.5 items-center text-sm">
          <MapPinIcon className="size-3" />
          <span>{location}</span>
          <span>•</span>
          <span>{locationType}</span>
        </div>
      )}

      <div>{description}</div>
    </div>
  );
}
