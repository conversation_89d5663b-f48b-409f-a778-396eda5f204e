import type { Metadata } from 'next';

import { CardList } from '@/components/card-list';
import { MDXContent } from '@/components/mdx-content';
import { CalendarIcon, MapPinIcon } from 'lucide-react';

import { sortExperienceDesc } from '@/utils';
import { formatExperienceDate } from '@/utils/date';
import { type Experience, experience } from '@/velite';

export const metadata: Metadata = {
  title: 'Experience',
  description: 'A summary of my work experience and career journey.',
};

export default function ExperiencePage() {
  return (
    <div className="min-h-[50dvh]">
      <p className="text-secondary-foreground mb-5">
        A summary of my work experience and career journey.
      </p>

      <CardList>
        {experience.sort(sortExperienceDesc).map((exp) => (
          <ExperienceCard key={exp.id} experience={exp} />
        ))}
      </CardList>
    </div>
  );
}

function ExperienceCard(props: { experience: Experience }) {
  const { title, position, location, locationType, startDate, endDate, body } =
    props.experience;

  return (
    <div className="flex flex-col gap-2">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2">
        <div>
          <span className="font-medium">{title}</span>
          <span className="block text-sm">{position}</span>
        </div>

        <div className="flex items-center gap-1 text-sm text-secondary-foreground">
          <CalendarIcon className="size-4" />
          <span>
            {formatExperienceDate(startDate)} -{' '}
            {endDate ? formatExperienceDate(endDate) : 'Present'}
          </span>
        </div>
      </div>

      {location && (
        <div className="flex items-center gap-2 text-sm text-secondary-foreground">
          <MapPinIcon className="size-4" />
          <span>
            {location} • {locationType}
          </span>
        </div>
      )}

      <div className="mt-2">
        <MDXContent code={body} />
      </div>
    </div>
  );
}
