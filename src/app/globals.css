@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 60 3% 6%;

    /* dark
    --foreground: 0 0% 90%;
    --background: 60 3% 6%;
    --secondary-foreground: 0 0% 64%; */

    --card: 0 0% 100%;
    --card-foreground: 60 3% 6%;

    --popover: 0 0% 100%;
    --popover-foreground: 60 3% 6%;

    --primary: 220.9 39.3% 11%;
    --primary-foreground: 210 20% 98%;

    --secondary: 220 14.3% 95%;
    --secondary-foreground: 0 0% 50%;

    --muted: 220 14.3% 95.9%;
    --muted-foreground: 220 8.9% 46.1%;

    --accent: 0 0% 90%;
    --accent-foreground: 220.9 39.3% 11%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 20% 98%;

    --border: 220 13% 91%;
    --input: 220 13% 91%;
    --ring: 60 3% 6%;

    --radius: 0.5rem;

    --sh-class: #7aa2f7;
    --sh-sign: #89ddff;
    --sh-string: #9ece6a;
    --sh-keyword: #bb9af7;
    --sh-comment: #565f89;
    --sh-jsxliterals: #7aa2f7;
    --sh-property: #73daca;
    --sh-entity: #e0af68;
  }

  .dark {
    --sh-class: #7aa2f7;
    --sh-sign: #89ddff;
    --sh-string: #9ece6a;
    --sh-keyword: #bb9af7;
    --sh-comment: #565f89;
    --sh-jsxliterals: #7aa2f7;
    --sh-property: #73daca;
    --sh-entity: #e0af68;

    /* --background: 60 3% 6%; */
    --background: 0 0% 12%;
    --foreground: 0 0% 90%;

    --card: 60 3% 6%;
    --card-foreground: 0 0% 90%;

    --popover: 60 3% 6%;
    --popover-foreground: 0 0% 90%;

    --primary: 0 0% 90%;
    --primary-foreground: 60 3% 6%;

    --secondary: 0 0% 22%;
    /* --secondary-foreground: 0 0% 64%; */
    --secondary-foreground: 0 0% 53%;

    --muted: 0 0% 15%;
    --muted-foreground: 0 0% 64%;

    --accent: 0 0% 15%;
    --accent-foreground: 0 0% 90%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 20% 98%;

    --border: 0 0% 15%;
    --input: 0 0% 15%;
    --ring: 0 0% 90%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  html {
    @apply bg-background text-foreground;
  }
}
