import type { MetadataRoute } from 'next';

import { SiteConfig } from '@/configuration';

const siteUrl = SiteConfig.site.url;

export default function robots(): MetadataRoute.Robots {
  return {
    rules: [
      {
        userAgent: '*',
        allow: ['/', '/experience', '/projects', '/blog', '/contact'],
        disallow: ['/*.json', '/*.xml'],
      },
    ],
    sitemap: `${siteUrl}/sitemap.xml`,
    host: siteUrl,
  };
}
