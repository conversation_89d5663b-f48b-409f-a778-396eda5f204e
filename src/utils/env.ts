import { z } from 'zod'

/**
 * Specify your server-side environment variables schema here. This way you can ensure the app isn't
 * built with invalid env vars.
 */
const serverSchema = z.object({
  NODE_ENV: z.enum(['development', 'test', 'production']),
  AUTH_SECRET: z.string(),
  AUTH_GOOGLE_ID: z.string(),
  AUTH_GOOGLE_SECRET: z.string(),
  POSTGRES_URL: z.string().url(),
})

/**
 * Specify your client-side environment variables schema here. This way you can ensure the app isn't
 * built with invalid env vars. To expose them to the client, prefix them with `NEXT_PUBLIC_`.
 */
const clientSchema = z.object({
  NEXT_PUBLIC_SITE_URL: z.string().url(),
})

/**
 * Define the type for your merged environment variables.
 */
type MergedEnvVars = z.infer<typeof serverSchema> & z.infer<typeof clientSchema>

/**
 * You can't destruct `process.env` as a regular object in the Next.js edge runtimes (e.g.
 * middlewares) or client-side so we need to destruct manually.
 */
const processEnv: Record<keyof MergedEnvVars, string | undefined> = {
  // Server Keys
  NODE_ENV: process.env.NODE_ENV,
  AUTH_SECRET: process.env.AUTH_SECRET,
  AUTH_GOOGLE_ID: process.env.AUTH_GOOGLE_ID,
  AUTH_GOOGLE_SECRET: process.env.AUTH_GOOGLE_SECRET,
  // Client Keys
  POSTGRES_URL: process.env.POSTGRES_URL,
  NEXT_PUBLIC_SITE_URL: process.env.NEXT_PUBLIC_SITE_URL,
}

// Don't touch the part below
// --------------------------

const mergedSchema = serverSchema.merge(clientSchema)

let env = process.env as MergedEnvVars

if (!process.env.SKIP_ENV_VALIDATION) {
  const isServer = typeof window === 'undefined'

  const parsed = isServer
    ? mergedSchema.safeParse(processEnv) // on server we can validate all env vars
    : clientSchema.safeParse(processEnv) // on client we can only validate the ones that are exposed

  if (!parsed.success) {
    console.error(
      '❌ Invalid environment variables:',
      parsed.error.flatten().fieldErrors
    )
    throw new Error('Invalid environment variables')
  }

  env = parsed.data as MergedEnvVars
}

export { env }
