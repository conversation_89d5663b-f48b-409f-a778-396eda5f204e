import { Dr<PERSON>zleAdapter } from '@auth/drizzle-adapter'
import NextAuth from 'next-auth'
import Google from 'next-auth/providers/google'

import { env } from '@/utils/env'
import { getLogger } from '@/utils/logger'
import { db } from '@/database'

const logger = getLogger()

export const {
  handlers,
  auth: getSession,
  signIn,
  signOut,
} = NextAuth({
  adapter: DrizzleAdapter(db),
  providers: [Google],
  callbacks: {
    async session({ session, token }) {
      return session
    },
  },
  logger: {
    error(error) {
      logger.error({ error })
    },
    warn(warn) {
      logger.warn({ warn })
    },
    debug(code) {
      logger.debug(code)
    },
  },
  debug: env.NODE_ENV === 'development',
})
